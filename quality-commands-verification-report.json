{"timestamp": "2025-07-30T03:15:26.097Z", "summary": {"total": 28, "passed": 25, "failed": 3, "successRate": 89}, "results": [{"command": "pnpm type-check:strict", "status": "PASS", "note": "执行成功"}, {"command": "pnpm lint:check", "status": "PASS", "note": "执行成功"}, {"command": "pnpm format:check", "status": "FAIL", "note": "Command failed: pnpm format:check"}, {"command": "pnpm build", "status": "PASS", "note": "命令已定义"}, {"command": "pnpm test", "status": "PASS", "note": "执行成功"}, {"command": "pnpm arch:validate", "status": "PASS", "note": "执行成功"}, {"command": "pnpm security:check", "status": "PASS", "note": "命令已定义"}, {"command": "pnpm duplication:check", "status": "PASS", "note": "执行成功"}, {"command": "pnpm size:check", "status": "PASS", "note": "执行成功"}, {"command": "pnpm ui:test", "status": "PASS", "note": "执行成功"}, {"command": "pnpm docs:validate", "status": "PASS", "note": "执行成功"}, {"command": "pnpm deploy:test", "status": "PASS", "note": "执行成功"}, {"command": "pnpm analytics:test", "status": "PASS", "note": "执行成功"}, {"command": "pnpm integration:test", "status": "PASS", "note": "执行成功"}, {"command": "pnpm dev:test", "status": "PASS", "note": "执行成功"}, {"command": "pnpm a11y:test", "status": "PASS", "note": "执行成功"}, {"command": "pnpm wcag:validate", "status": "PASS", "note": "执行成功"}, {"command": "pnpm quality:report", "status": "FAIL", "note": "spawnSync /bin/sh ETIMEDOUT"}, {"command": "pnpm complexity:check", "status": "PASS", "note": "执行成功"}, {"command": "pnpm test:ai-validation", "status": "PASS", "note": "执行成功"}, {"command": "pnpm test:architecture", "status": "PASS", "note": "执行成功"}, {"command": "pnpm test:security-boundaries", "status": "PASS", "note": "执行成功"}, {"command": "pnpm type-safety:check", "status": "PASS", "note": "执行成功"}, {"command": "pnpm unsafe:detect", "status": "PASS", "note": "执行成功"}, {"command": "pnpm quality:monitor", "status": "FAIL", "note": "Command failed: pnpm quality:monitor"}, {"command": "pnpm performance:check", "status": "PASS", "note": "执行成功"}, {"command": "pnpm lighthouse:ci", "status": "PASS", "note": "执行成功"}, {"command": "pnpm renovate:validate", "status": "PASS", "note": "执行成功"}]}