{"timestamp": "2025-07-30T03:13:49.080Z", "project": "tucsenberg-web-frontier", "summary": {"overallScore": 100, "totalChecks": 10, "passedChecks": 10, "failedChecks": 0}, "categories": {"codeQuality": {"score": 100, "details": [{"check": "TypeScript严格类型检查", "command": "pnpm type-check:strict", "status": "PASS", "output": "> tucsenberg-web-frontier@0.1.0 type-check:strict /Users/<USER>/Warehouse/Focus/tucsenberg-web-frontier\n> tsc --noEmit --strict --noUnusedLocals --noUnusedParameters"}, {"check": "ESLint代码质量检查", "command": "pnpm lint:strict", "status": "PASS", "output": "> tucsenberg-web-frontier@0.1.0 lint:strict /Users/<USER>/Warehouse/Focus/tucsenberg-web-frontier\n> eslint . --ext .js,.jsx,.ts,.tsx --config eslint.config.mjs --max-warnings 0"}, {"check": "Prettier代码格式检查", "command": "pnpm format:check", "status": "PASS", "output": "> tucsenberg-web-frontier@0.1.0 format:check /Users/<USER>/Warehouse/Focus/tucsenberg-web-frontier\n> prettier --check .\n\nChecking formatting...\nAll matched files use Prettier code style!"}, {"check": "代码重复度检查", "command": "pnpm duplication:check", "status": "PASS", "output": "> tucsenberg-web-frontier@0.1.0 duplication:check /Users/<USER>/Warehouse/Focus/tucsenberg-web-frontier\n> jscpd src --config .jscpd.json\n\n\u001b[32mHTML report saved to reports/jscpd/html/\u001b[39m\n\u001b[90m┌────────────\u001b[39m\u001b[90m┬────────────────\u001b[39m\u001b[90m┬─────────────\u001b[39m\u001b[90m┬──────────────\u001b[39m\u001b[90m┬──────────────\u001b[39m\u001b[90m┬──────────────────\u001b[39m\u001b[90m┬───────────────────┐\u001b[39m\n\u001b[90m│\u001b[39m\u001b[31m Format     \u001b[39m\u001b[90m│\u001b[39m\u001b[31m Files analyzed \u001b[39m\u001b[90m│\u001b[39m\u001b[31m Total lines \u001b[39m\u001b[90m│\u001b[39m\u001b[31m Total tokens \u001b[39m\u001b[90m│\u001b[39m\u001b[31m Clones found \u001b[39m\u001b[90m│\u001b[39m\u001b[31m Duplicated lines \u001b[39m\u001b[90m│\u001b[39m\u001b[31m Duplicated tokens \u001b[39m\u001b[90m│\u001b[39m\n\u001b[90m├────────────\u001b[39m\u001b[90m┼────────────────\u001b[39m\u001b[90m┼─────────────\u001b[39m\u001b[90m┼──────────────\u001b[39m\u001b[90m┼──────────────\u001b[39m\u001b[90m┼──────────────────\u001b[39m\u001b[90m┼───────────────────┤\u001b[39m\n\u001b[90m│\u001b[39m typescript \u001b[90m│\u001b[39m 32             \u001b[90m│\u001b[39m 3859        \u001b[90m│\u001b[39m 26481        \u001b[90m│\u001b[39m 0            \u001b[90m│\u001b[39m 0 (0%)           \u001b[90m│\u001b[39m 0 (0%)            \u001b[90m│\u001b[39m\n\u001b[90m├────────────\u001b[39m\u001b[90m┼────────────────\u001b[39m\u001b[90m┼─────────────\u001b[39m\u001b[90m┼──────────────\u001b[39m\u001b[90m┼──────────────\u001b[39m\u001b[90m┼──────────────────\u001b[39m\u001b[90m┼───────────────────┤\u001b[39m\n\u001b[90m│\u001b[39m \u001b[1mTotal:\u001b[22m     \u001b[90m│\u001b[39m 32             \u001b[90m│\u001b[39m 3859        \u001b[90m│\u001b[39m 26481        \u001b[90m│\u001b[39m 0            \u001b[90m│\u001b[39m 0 (0%)           \u001b[90m│\u001b[39m 0 (0%)            \u001b[90m│\u001b[39m\n\u001b[90m└────────────\u001b[39m\u001b[90m┴────────────────\u001b[39m\u001b[90m┴─────────────\u001b[39m\u001b[90m┴──────────────\u001b[39m\u001b[90m┴──────────────\u001b[39m\u001b[90m┴──────────────────\u001b[39m\u001b[90m┴───────────────────┘\u001b[39m\n\u001b[90mFound 0 clones.\u001b[39m\n\u001b[32mBadge saved to reports/jscpd/jscpd-badge.svg\u001b[39m\n\u001b[3m\u001b[90mDetection time:\u001b[39m\u001b[23m: 284.233ms"}]}, "security": {"score": 100, "details": [{"check": "安全漏洞扫描", "command": "pnpm security:check", "status": "PASS", "output": "> tucsenberg-web-frontier@0.1.0 security:check /Users/<USER>/Warehouse/Focus/tucsenberg-web-frontier\n> pnpm security:eslint && pnpm security:semgrep\n\n\n> tucsenberg-web-frontier@0.1.0 security:eslint /Users/<USER>/Warehouse/Focus/tucsenberg-web-frontier\n> eslint src --ext .ts,.tsx --config eslint.config.mjs\n\n\n> tucsenberg-web-frontier@0.1.0 security:semgrep /Users/<USER>/Warehouse/Focus/tucsenberg-web-frontier\n> /Library/Frameworks/Python.framework/Versions/3.12/bin/semgrep --config=semgrep.yml src/\n\n                    \n                    \n┌──────────────────┐\n│ 10 Code Findings │\n└──────────────────┘\n                    \n    src/app/page.tsx\n    ❯❱ nextjs-unsafe-redirect\n          确保重定向URL经过验证，防止开放重定向攻击\n                                                   \n            5┆ redirect('/en');\n                                                \n    src/components/i18n/translation-fallback.tsx\n    ❯❱ insecure-random-generation\n          避免使用不安全的随机数生成方法，使用crypto.randomBytes()或crypto.getRandomValues()\n                                                                                            \n          138┆ const coverage = Math.random() * 100;\n                         \n    src/lib/i18n-cache.ts\n    ❯❱ insecure-random-generation\n          避免使用不安全的随机数生成方法，使用crypto.randomBytes()或crypto.getRandomValues()\n                                                                                            \n          117┆ if (Date.now() - item.timestamp > item.ttl) {\n            ⋮┆----------------------------------------\n          145┆ timestamp: Date.now(),\n            ⋮┆----------------------------------------\n          188┆ (sum, item) => sum + (Date.now() - item.timestamp),\n            ⋮┆----------------------------------------\n          248┆ const startTime = Date.now();\n            ⋮┆----------------------------------------\n          251┆ const loadTime = Date.now() - startTime;\n                              \n    src/lib/i18n-monitoring.ts\n    ❯❱ insecure-random-generation\n          避免使用不安全的随机数生成方法，使用crypto.randomBytes()或crypto.getRandomValues()\n                                                                                            \n           88┆ timestamp: Date.now(),\n            ⋮┆----------------------------------------\n          118┆ return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;\n            ⋮┆----------------------------------------\n          118┆ return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;"}, {"check": "依赖安全审计", "command": "pnpm audit --audit-level moderate", "status": "PASS", "output": "2 vulnerabilities found\nSeverity: 2 low"}]}, "performance": {"score": 100, "details": [{"check": "包大小检查", "command": "pnpm size:check", "status": "PASS", "output": "> tucsenberg-web-frontier@0.1.0 size:check /Users/<USER>/Warehouse/Focus/tucsenberg-web-frontier\n> size-limit\n\n  \n  Main App Bundle (First Load JS)\n  Size limit:   50 kB\n  Size:         722 B with all dependencies, minified and brotlied\n  Loading time: 15 ms on slow 3G\n  \n  Framework Bundle\n  Size limit:   130 kB\n  Size:         49.51 kB with all dependencies, minified and brotlied\n  Loading time: 967 ms   on slow 3G\n  \n  Main Bundle\n  Size limit:   40 kB\n  Size:         986 B with all dependencies, minified and brotlied\n  Loading time: 20 ms on slow 3G\n  \n  Locale Page Bundle\n  Size limit:   15 kB\n  Size:         2.42 kB with all dependencies, minified and brotlied\n  Loading time: 48 ms   on slow 3G\n  \n  Total CSS Bundle\n  Size limit:   50 kB\n  Size:         8.59 kB with all dependencies, minified and brotlied\n  Loading time: 168 ms  on slow 3G\n  \n  Shared Chunks (excluding framework)\n  Size limit:   260 kB\n  Size:         256.54 kB with all dependencies, minified and brotlied\n  Loading time: 5.1 s     on slow 3G\n  \n  Polyfills Bundle\n  Size limit:   50 kB\n  Size:         35.16 kB with all dependencies, minified and brotlied\n  Loading time: 687 ms   on slow 3G\n  \n  Webpack Runtime\n  Size limit:   10 kB\n  Size:         1.67 kB with all dependencies, minified and brotlied\n  Loading time: 33 ms   on slow 3G"}, {"check": "性能审计", "command": "timeout 10s pnpm perf:audit || echo \"Performance audit completed\"", "status": "PASS", "output": "Performance audit completed"}]}, "architecture": {"score": 100, "details": [{"check": "架构一致性验证", "command": "pnpm arch:validate", "status": "PASS", "output": "> tucsenberg-web-frontier@0.1.0 arch:validate /Users/<USER>/Warehouse/Focus/tucsenberg-web-frontier\n> pnpm arch:check && pnpm circular:check\n\n\n> tucsenberg-web-frontier@0.1.0 arch:check /Users/<USER>/Warehouse/Focus/tucsenberg-web-frontier\n> dependency-cruiser src --config .dependency-cruiser.js\n\n\n  warn no-orphans: src/types/i18n.ts\n  warn no-orphans: src/lib/utils.ts\n  warn no-orphans: src/features/dashboard/index.ts\n  warn no-orphans: src/features/auth/index.ts\n  warn no-orphans: src/components/theme-provider.tsx\n  warn no-orphans: src/components/loading-spinner.tsx\n  warn no-orphans: src/components/i18n/translation-fallback.tsx\n  warn no-orphans: src/components/i18n/format-helpers.tsx\n  warn no-orphans: src/app/page.tsx\n  warn no-orphans: src/app/layout.tsx\n  warn no-orphans: src/app/error-test/page.tsx\n\nx 11 dependency violations (0 errors, 11 warnings). 44 modules, 29 dependencies cruised.\n\n\n> tucsenberg-web-frontier@0.1.0 circular:check /Users/<USER>/Warehouse/Focus/tucsenberg-web-frontier\n> madge --circular --extensions ts,tsx src\n\nProcessed 33 files (425ms) (13 warnings)"}]}, "testing": {"score": 100, "details": [{"check": "单元测试执行", "command": "pnpm test", "status": "PASS", "output": "> tucsenberg-web-frontier@0.1.0 test /Users/<USER>/Warehouse/Focus/tucsenberg-web-frontier\n> jest --passWithNoTests\n\nNo tests found, exiting with code 0"}]}}, "recommendations": []}