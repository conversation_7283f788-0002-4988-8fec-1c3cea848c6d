# 项目质量报告

**生成时间**: 2025-07-30T03:50:00.376Z
**项目**: tucsenberg-web-frontier

## 📊 总体评分

**总分**: 95/100
**检查项**: 10 (通过: 9, 失败: 1)

## 📋 分类评分

⚠️ **codeQuality**: 75/100
✅ **security**: 100/100
✅ **performance**: 100/100
✅ **architecture**: 100/100
✅ **testing**: 100/100

## 🔍 详细检查结果

### codeQuality

✅ TypeScript严格类型检查
✅ ESLint代码质量检查
❌ Prettier代码格式检查
   错误: Command failed: pnpm format:check
✅ 代码重复度检查

### security

✅ 安全漏洞扫描
✅ 依赖安全审计

### performance

✅ 包大小检查
✅ 性能审计

### architecture

✅ 架构一致性验证

### testing

✅ 单元测试执行

## 💡 改进建议

- 建议改进代码质量：修复ESLint警告，优化TypeScript类型定义
