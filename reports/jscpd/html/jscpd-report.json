{"statistics": {"detectionDate": "2025-07-30T03:50:06.140Z", "formats": {"typescript": {"sources": {"/Users/<USER>/Warehouse/Focus/tucsenberg-web-frontier/src/features/dashboard/index.ts": {"lines": 8, "tokens": 54, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "/Users/<USER>/Warehouse/Focus/tucsenberg-web-frontier/src/features/auth/index.ts": {"lines": 8, "tokens": 59, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "/Users/<USER>/Warehouse/Focus/tucsenberg-web-frontier/src/components/ui/sheet.tsx": {"lines": 159, "tokens": 977, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "/Users/<USER>/Warehouse/Focus/tucsenberg-web-frontier/src/components/ui/navigation-menu.tsx": {"lines": 168, "tokens": 948, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "/Users/<USER>/Warehouse/Focus/tucsenberg-web-frontier/src/components/ui/label.tsx": {"lines": 24, "tokens": 138, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "/Users/<USER>/Warehouse/Focus/tucsenberg-web-frontier/src/components/ui/input.tsx": {"lines": 20, "tokens": 130, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "/Users/<USER>/Warehouse/Focus/tucsenberg-web-frontier/src/components/ui/dropdown-menu.tsx": {"lines": 275, "tokens": 1562, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "/Users/<USER>/Warehouse/Focus/tucsenberg-web-frontier/src/components/ui/card.tsx": {"lines": 91, "tokens": 576, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "/Users/<USER>/Warehouse/Focus/tucsenberg-web-frontier/src/components/ui/button.tsx": {"lines": 59, "tokens": 366, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "/Users/<USER>/Warehouse/Focus/tucsenberg-web-frontier/src/components/i18n/translation-fallback.tsx": {"lines": 197, "tokens": 1372, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "/Users/<USER>/Warehouse/Focus/tucsenberg-web-frontier/src/components/i18n/format-helpers.tsx": {"lines": 215, "tokens": 1872, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "/Users/<USER>/Warehouse/Focus/tucsenberg-web-frontier/src/app/error-test/page.tsx": {"lines": 234, "tokens": 1636, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "/Users/<USER>/Warehouse/Focus/tucsenberg-web-frontier/src/app/[locale]/page.tsx": {"lines": 222, "tokens": 1414, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "/Users/<USER>/Warehouse/Focus/tucsenberg-web-frontier/src/app/[locale]/layout.tsx": {"lines": 72, "tokens": 468, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "/Users/<USER>/Warehouse/Focus/tucsenberg-web-frontier/src/types/index.ts": {"lines": 12, "tokens": 23, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "/Users/<USER>/Warehouse/Focus/tucsenberg-web-frontier/src/types/i18n.ts": {"lines": 310, "tokens": 2052, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "/Users/<USER>/Warehouse/Focus/tucsenberg-web-frontier/src/types/global.ts": {"lines": 154, "tokens": 717, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "/Users/<USER>/Warehouse/Focus/tucsenberg-web-frontier/src/shared/utils.ts": {"lines": 34, "tokens": 84, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "/Users/<USER>/Warehouse/Focus/tucsenberg-web-frontier/src/lib/utils.ts": {"lines": 5, "tokens": 61, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "/Users/<USER>/Warehouse/Focus/tucsenberg-web-frontier/src/lib/index.ts": {"lines": 12, "tokens": 23, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "/Users/<USER>/Warehouse/Focus/tucsenberg-web-frontier/src/lib/i18n-validation.ts": {"lines": 346, "tokens": 2595, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "/Users/<USER>/Warehouse/Focus/tucsenberg-web-frontier/src/lib/i18n-monitoring.ts": {"lines": 434, "tokens": 3339, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "/Users/<USER>/Warehouse/Focus/tucsenberg-web-frontier/src/lib/i18n-cache.ts": {"lines": 351, "tokens": 2825, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "/Users/<USER>/Warehouse/Focus/tucsenberg-web-frontier/src/i18n/routing.ts": {"lines": 44, "tokens": 235, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "/Users/<USER>/Warehouse/Focus/tucsenberg-web-frontier/src/i18n/request.ts": {"lines": 45, "tokens": 326, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "/Users/<USER>/Warehouse/Focus/tucsenberg-web-frontier/src/components/theme-toggle.tsx": {"lines": 44, "tokens": 358, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "/Users/<USER>/Warehouse/Focus/tucsenberg-web-frontier/src/components/theme-provider.tsx": {"lines": 33, "tokens": 207, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "/Users/<USER>/Warehouse/Focus/tucsenberg-web-frontier/src/components/loading-spinner.tsx": {"lines": 48, "tokens": 330, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "/Users/<USER>/Warehouse/Focus/tucsenberg-web-frontier/src/components/language-toggle.tsx": {"lines": 123, "tokens": 962, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "/Users/<USER>/Warehouse/Focus/tucsenberg-web-frontier/src/components/error-boundary.tsx": {"lines": 97, "tokens": 674, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "/Users/<USER>/Warehouse/Focus/tucsenberg-web-frontier/src/app/page.tsx": {"lines": 5, "tokens": 36, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "/Users/<USER>/Warehouse/Focus/tucsenberg-web-frontier/src/app/layout.tsx": {"lines": 10, "tokens": 62, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}}, "total": {"lines": 3859, "tokens": 26481, "sources": 32, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}}}, "total": {"lines": 3859, "tokens": 26481, "sources": 32, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}}, "duplicates": [], "filename": "/Users/<USER>/Warehouse/Focus/tucsenberg-web-frontier/node_modules/.pnpm/@jscpd+html-reporter@4.0.1/node_modules/@jscpd/html-reporter/dist/templates/main.pug"}