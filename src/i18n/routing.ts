import { createNavigation } from 'next-intl/navigation';
import { defineRouting } from 'next-intl/routing';

export const routing = defineRouting({
  // A list of all locales that are supported
  locales: ['en', 'zh'],

  // Used when no locale matches
  defaultLocale: 'en',

  // The prefix for the default locale
  localePrefix: 'always',

  // Alternative hosts for different locales
  // domains: [
  //   {
  //     domain: 'example.com',
  //     defaultLocale: 'en'
  //   },
  //   {
  //     domain: 'example.cn',
  //     defaultLocale: 'zh'
  //   }
  // ],

  // Pathnames that can be localized
  pathnames: {
    '/': '/',
    '/about': {
      en: '/about',
      zh: '/guanyu',
    },
    '/contact': {
      en: '/contact',
      zh: '/lianxi',
    },
  },
});

// Lightweight wrappers around Next.js' navigation APIs
// that will consider the routing configuration
export const { Link, redirect, usePathname, useRouter } =
  createNavigation(routing);

export type Locale = (typeof routing.locales)[number];
